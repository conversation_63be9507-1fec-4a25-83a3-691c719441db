# ✨ Neeharika's Personal Portfolio

A modern, responsive portfolio built with [**Next.js**](https://nextjs.org) + [**Tailwind CSS**](https://tailwindcss.com), boot-strapped via [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## 🌐 Live Portfolio URL

[![Live Demo](https://img.shields.io/badge/demo-online-brightgreen?logo=vercel)](https://portfolio-website-neeharika.vercel.app/)


## ✨ Features

| Category | What you get |
| -------- | ------------ |
| **Modern stack** | Next .js 14 (App Router) + TypeScript + Tailwind v3.4 |
| **Page sections** | Hero, About, Tech Skills, Education, Achievements, Projects (dynamic), Contact |
| **Dark-mode toggle** | Theme stored in `localStorage`, toggled with Headless UI switch |
| **Framer Motion animations** | Smooth page‐fade and element entrance effects |
| **SEO-ready** | Title/description/Open-Graph meta via `app/layout.tsx` |
| **Fully responsive** | Mobile-first design & CSS grid for project cards |

---

## 🚀 Getting Started (Local Dev)

```bash
# Install dependencies
npm install

# Run the dev server:
npm run dev

# Open in browser:
http://localhost:3000
```

## 🚀 Deploy & Host on Vercel

### One-click (Dashboard)
####	1.	Sign in to Vercel with your GitHub account.
####	2.	Click “New Project → Import Git Repository.”
####	3.	Select "portfolio-website-neeharika" which is the name of the repository. Vercel auto-detects Next.js.
####	4.	Keep the default build settings (npm install, next build) and click Deploy.
####	5.	Wait ~60 s. When the build finishes, your site is live at https://portfolio-website-neeharika.vercel.app (or a similar slug).
