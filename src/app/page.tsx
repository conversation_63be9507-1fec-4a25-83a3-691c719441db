"use client";

import { useState, useRef } from "react";

export default function Home() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const achievementsRef = useRef<HTMLDivElement>(null);

  const achievements = ['technovation', 'microsoft', 'guinness', 'sun', 'agile', 'academic'];

  const scrollToIndex = (index: number) => {
    if (achievementsRef.current) {
      const cardWidth = 320 + 32;
      achievementsRef.current.scrollTo({
        left: index * cardWidth,
        behavior: 'smooth'
      });
      setCurrentIndex(index);
    }
  };

  const goToPrevious = () => {
    const newIndex = Math.max(0, currentIndex - 2);
    scrollToIndex(newIndex);
  };

  const goToNext = () => {
    const maxIndex = Math.max(0, achievements.length - 2);
    const newIndex = Math.min(maxIndex, currentIndex + 2);
    scrollToIndex(newIndex);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm z-50 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-xl font-semibold text-slate-800 dark:text-white">
              Neeharika Vemulapati
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#about" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a>
              <a href="#experience" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Experience</a>
              <a href="#education" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Education</a>
              <a href="#achievements" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Achievements</a>
              <a href="#skills" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Skills</a>
              <a href="#projects" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Projects</a>
              <a href="#contact" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Contact</a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-40 h-40 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-1 animate-pulse hover:animate-spin transition-all duration-500 group">
                <div className="w-full h-full rounded-full overflow-hidden bg-slate-100 dark:bg-slate-800 hover:bg-white dark:hover:bg-slate-700 transition-colors duration-300">
                  <img
                    src="/neeharika-photo.jpg"
                    alt="Neeharika Vemulapati - Senior Software Engineer"
                    className="w-full h-full object-cover rounded-full group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-4 text-slate-900 dark:text-white animate-fade-in-up">
                Hi, I'm <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Neeharika Vemulapati</span>
                <span className="inline-block animate-bounce ml-2">👋</span>
              </h1>
            </div>

            <div className="space-y-4 mb-12 animate-fade-in-up animation-delay-500">
              <p className="text-xl sm:text-2xl text-slate-600 dark:text-slate-300 font-medium">
                💻 Senior Software Engineer
              </p>

              <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 px-6 py-3 rounded-full border border-blue-200 dark:border-blue-800">
                <span className="text-lg text-blue-700 dark:text-blue-300 font-semibold">
                  🏆 Microsoft AI Hackathon 2025 Winner
                </span>
                <span className="ml-2 animate-pulse">✨</span>
              </div>

              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed">
                🚀 Passionate about building innovative software solutions with expertise in
                <span className="font-semibold text-blue-600 dark:text-blue-400"> Java, Python, AWS</span>, and modern web technologies.
                Currently leading <span className="font-semibold text-purple-600 dark:text-purple-400">digital payments transformation initiatives</span> at CSAA Insurance Group.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up animation-delay-1000">
              <a
                href="#contact"
                className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="flex items-center justify-center">
                  📬 Get In Touch
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </a>
              <a
                href="#projects"
                className="group border-2 border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="flex items-center justify-center">
                  🚀 View Projects
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              🌟 About Me
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Senior Software Engineer with 7+ years of experience at Fortune 500 companies,
              specializing in enterprise applications, cloud architecture, and AI innovation. 💡
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                🚀 My Journey
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                My career spans from QA automation to senior engineering roles at AWS, American Express,
                FedEx, and CSAA Insurance Group. I've consistently earned recognition for technical
                excellence and leadership, including winning Microsoft's "Best Python Agent" award in 2025.
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Currently leading digital payments transformation at CSAA Insurance Group, I architect
                enterprise solutions using Java, Spring Boot, Python, and AWS. My expertise includes
                microservices, containerization, and modern CI/CD pipelines.
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                I'm passionate about AI innovation and contribute to healthcare technology through
                volunteer work at Phoenix Children's Hospital, building HIPAA-compliant solutions.
              </p>
            </div>

            <div className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg">
                <h4 className="font-semibold text-slate-900 dark:text-white mb-3">🎯 Current Focus</h4>
                <ul className="text-slate-600 dark:text-slate-300 space-y-2">
                  <li>💳 Digital Payments Transformation</li>
                  <li>☕ Enterprise Java & Spring Boot</li>
                  <li>☁️ AWS Cloud Architecture</li>
                  <li>🤖 AI Agents & Machine Learning</li>
                </ul>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg">
                <h4 className="font-semibold text-slate-900 dark:text-white mb-3">🏆 Key Achievements</h4>
                <ul className="text-slate-600 dark:text-slate-300 space-y-2">
                  <li>🥇 Microsoft AI Hackathon Winner (2025)</li>
                  <li>🌟 2x SUN Award Winner (ASU)</li>
                  <li>👑 Agile Leadership Award (FedEx)</li>
                  <li>🎓 4.0 GPA Master's Degree</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Work Experience Section */}
      <section id="experience" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Work Experience
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              My professional journey and the impact I've made in various roles.
            </p>
          </div>

          <div className="space-y-8">
            {/* Experience 1 - CSAA Insurance Group */}
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-700 p-6 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium animate-pulse">Current</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Feb 2025 - Present</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Senior Software Engineer</h3>
              <p className="text-blue-600 dark:text-blue-400 font-semibold mb-4">CSAA Insurance Group</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Leading digital payments transformation initiatives and developing scalable insurance technology solutions.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Java</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Spring Boot</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">AWS</span>
              </div>
            </div>

            {/* Experience 2 - American Express (Recent) */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">Recent</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Jun 2024 - Jan 2025</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Software Development Engineer</h3>
              <p className="text-blue-600 dark:text-blue-400 font-semibold mb-4">American Express</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Developed enterprise-scale financial applications and payment processing systems serving millions of customers.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Java</span>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs">Python</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Spring Boot</span>
              </div>
            </div>

            {/* Experience 3 - American Express (Previous) */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-slate-200 px-3 py-1 rounded-full text-sm font-medium">Previous</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Aug 2023 - Mar 2024</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Software Development Engineer</h3>
              <p className="text-blue-600 dark:text-blue-400 font-semibold mb-4">American Express</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Built scalable microservices and APIs for financial services, focusing on performance optimization and security.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Java</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Spring Boot</span>
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-xs">Microservices</span>
              </div>
            </div>

            {/* Experience 4 - EPICS Volunteer */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">Volunteer</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Apr 2023 - Jul 2023</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Software Engineer (Volunteer)</h3>
              <p className="text-green-600 dark:text-green-400 font-semibold mb-2">EPICS at ASU</p>
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">Team: DocYou - Phoenix Children's Hospital</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Developed HIPAA-compliant healthcare solutions for Phoenix Children's Hospital, focusing on patient data management and security.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-xs">Healthcare</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">HIPAA</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Security</span>
              </div>
            </div>

            {/* Experience 5 - AWS */}
            <div className="bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-slate-800 dark:to-slate-700 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-orange-200 dark:border-orange-800">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium">AWS</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Sep 2022 - Mar 2023</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Software Development Engineer</h3>
              <p className="text-orange-600 dark:text-orange-400 font-semibold mb-4">Amazon Web Services - EBS Team</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Worked on Amazon Elastic Block Store (EBS) infrastructure, developing high-performance storage solutions for AWS cloud services.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-xs">AWS</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">EBS</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Cloud Storage</span>
              </div>
            </div>

            {/* Experience 6 - ASU QA */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">QA Lead</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Aug 2021 - May 2022</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">QA Automation Engineer</h3>
              <p className="text-yellow-600 dark:text-yellow-400 font-semibold mb-4">Arizona State University</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Led QA optimization reducing manual testing by 85% using Cypress and established Selenium automation framework.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Cypress</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Selenium</span>
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-xs">Test Automation</span>
              </div>
            </div>

            {/* Experience 7 - FedEx Technical Lead */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium">Tech Lead</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Jan 2021 - Aug 2021</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Technical Lead</h3>
              <p className="text-purple-600 dark:text-purple-400 font-semibold mb-4">FedEx</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Led technical initiatives and mentored development teams, implementing agile methodologies and best practices.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Leadership</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Agile</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Mentoring</span>
              </div>
            </div>

            {/* Experience 8 - FedEx Full Stack */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm font-medium">Full Stack</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">Jun 2020 - Jan 2021</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Full Stack Developer</h3>
              <p className="text-indigo-600 dark:text-indigo-400 font-semibold mb-4">FedEx</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Developed end-to-end logistics applications with modern web technologies and database optimization.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">React</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Node.js</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Full Stack</span>
              </div>
            </div>

            {/* Experience 9 - Scientific Games */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm font-medium">Intern</span>
                <span className="text-sm text-slate-500 dark:text-slate-400">May 2019 - Jul 2019</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Software Development Intern</h3>
              <p className="text-pink-600 dark:text-pink-400 font-semibold mb-4">Scientific Games</p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Contributed to gaming software development and learned industry best practices in software engineering.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-2 py-1 rounded text-xs">Gaming</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Software Dev</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Internship</span>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Achievements & Awards Section with Carousel */}
      <section id="achievements" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Achievements & Awards
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Recognition for excellence, leadership, and outstanding contributions throughout my career.
            </p>
          </div>

          <div className="relative">
            {/* Navigation Arrows */}
            <button
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 rounded-full p-3 shadow-lg border border-slate-200 dark:border-slate-600 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={goToPrevious}
              disabled={currentIndex === 0}
            >
              <svg className="w-6 h-6 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 rounded-full p-3 shadow-lg border border-slate-200 dark:border-slate-600 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={goToNext}
              disabled={currentIndex >= achievements.length - 2}
            >
              <svg className="w-6 h-6 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Achievements Container */}
            <div
              ref={achievementsRef}
              className="flex gap-8 overflow-x-hidden scroll-smooth"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                width: '672px', // Exactly 2 cards: (320px * 2) + (32px gap) = 672px
                margin: '0 auto'
              }}
            >
              {/* Technovation Girls Gold Judge - Latest Achievement */}
              <div className="group bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-2 border-amber-300 dark:border-amber-700 relative overflow-hidden flex-shrink-0 w-80">
                {/* New badge */}
                <div className="absolute top-4 right-4 bg-gradient-to-r from-amber-600 to-yellow-600 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                  NEW
                </div>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Gold Judge Recognition</h3>
                  <div className="mb-4">
                    <span className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 px-3 py-1 rounded-full text-sm font-medium">2025</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Technovation Girls</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Global Technology Competition Judge
                  </p>
                  <div className="bg-amber-100 dark:bg-amber-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Judged 11+ technology projects, contributing to over 14,500 scores globally and empowering girls to become tech leaders"
                    </p>
                  </div>
                </div>
              </div>

              {/* Microsoft AI Hackathon */}
              <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-2 border-blue-300 dark:border-blue-700 relative overflow-hidden flex-shrink-0 w-80">
                <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                  WINNER
                </div>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Best Python Agent</h3>
                  <div className="mb-4">
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">2025</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Microsoft AI Hackathon</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Global AI Competition
                  </p>
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Developed innovative AI agent using Python, recognized among thousands of global participants"
                    </p>
                  </div>
                </div>
              </div>

              {/* Guinness World Record */}
              <div className="group bg-gradient-to-br from-red-50 to-orange-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-2 border-red-300 dark:border-red-700 relative overflow-hidden flex-shrink-0 w-80">
                <div className="absolute top-4 right-4 bg-gradient-to-r from-red-600 to-orange-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                  WORLD RECORD
                </div>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Guinness World Record</h3>
                  <div className="mb-4">
                    <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm font-medium">2025</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Microsoft AI Skills Fest</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Most users taking online AI lesson in 24 hours
                  </p>
                  <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "One of 126,151 learners who achieved the Guinness World Record on April 8, 2025"
                    </p>
                  </div>
                </div>
              </div>

              {/* SUN Awards */}
              <div className="group bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 flex-shrink-0 w-80 relative overflow-hidden">
                <div className="absolute top-4 right-4 bg-gradient-to-r from-yellow-600 to-orange-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                  2x WINNER
                </div>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">SUN Awards</h3>
                  <div className="mb-4">
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">2022</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Arizona State University</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Excellence in Academic Performance
                  </p>
                  <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Recognized twice for outstanding academic achievements and contributions to university community"
                    </p>
                  </div>
                </div>
              </div>

              {/* Agile Leadership Award */}
              <div className="group bg-gradient-to-br from-purple-50 to-pink-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 flex-shrink-0 w-80">
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Agile Leadership Award</h3>
                  <div className="mb-4">
                    <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium">2021</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>FedEx</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Outstanding Leadership in Agile Methodologies
                  </p>
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Recognized for exceptional leadership in implementing agile practices and mentoring development teams"
                    </p>
                  </div>
                </div>
              </div>

              {/* Academic Excellence */}
              <div className="group bg-gradient-to-br from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 flex-shrink-0 w-80">
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Academic Excellence</h3>
                  <div className="mb-4">
                    <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">4.0 GPA</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Arizona State University</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Master's in Information Technology
                  </p>
                  <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Perfect 4.0 GPA throughout Master's program, demonstrating consistent academic excellence"
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Education Section */}
      <section id="education" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              🎓 Education
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              My academic background and achievements. 📚
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Master's Degree */}
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Master's in Information Technology</h3>
                <p className="text-blue-600 dark:text-blue-400 font-medium">Arizona State University</p>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium">4.0 GPA</span>
              </div>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                Advanced studies in Information Technology with perfect academic performance.
                Specialized in enterprise systems, cloud computing, and software architecture.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Enterprise Systems</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Cloud Computing</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Software Architecture</span>
              </div>
            </div>

            {/* Bachelor's Degree */}
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">BTech in Computer Engineering</h3>
                <p className="text-blue-600 dark:text-blue-400 font-medium">IIITDM Kancheepuram</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Indian Institute of Information Technology</p>
              </div>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                Comprehensive engineering education in Computer Engineering from a prestigious IIIT.
                Strong foundation in computer science fundamentals and programming.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-xs">Computer Engineering</span>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs">Programming</span>
                <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-xs">Algorithms</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              🛠️ Skills & Technologies
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Technologies and tools I work with to build innovative solutions. ⚡
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Programming Languages */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">💻</span>
                </div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Programming</h3>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-slate-600 dark:text-slate-300">Java</span>
                  <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{width: '95%'}}></div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-600 dark:text-slate-300">Python</span>
                  <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{width: '90%'}}></div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-600 dark:text-slate-300">JavaScript</span>
                  <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                    <div className="bg-yellow-600 h-2 rounded-full" style={{width: '85%'}}></div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-600 dark:text-slate-300">SQL</span>
                  <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                    <div className="bg-purple-600 h-2 rounded-full" style={{width: '88%'}}></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Frameworks */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Frameworks</h3>
              </div>
              <div className="space-y-3">
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm block text-center">Spring Boot</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm block text-center">React</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm block text-center">Flask</span>
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm block text-center">Express.js</span>
              </div>
            </div>

            {/* Cloud & DevOps */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">☁️</span>
                </div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Cloud & DevOps</h3>
              </div>
              <div className="space-y-3">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm block text-center">AWS</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm block text-center">Docker</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm block text-center">Jenkins</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm block text-center">Kubernetes</span>
              </div>
            </div>

            {/* Tools & Testing */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🔧</span>
                </div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Tools & Testing</h3>
              </div>
              <div className="space-y-3">
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm block text-center">Cypress</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm block text-center">Selenium</span>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm block text-center">Git</span>
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm block text-center">Jira</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Projects
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              A showcase of innovative solutions and technical achievements across various domains.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Project 1 - VCT Hackathon */}
            <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:-rotate-1 border border-blue-200 dark:border-blue-800">
              <div className="relative h-48 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl mb-2">🎮</div>
                  <div className="text-white font-bold text-lg">VCT Hackathon</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                  VALORANT Esports Team Optimization
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Advanced analytics platform using AWS, OpenSearch, and vector databases to optimize VALORANT esports team performance and strategy.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AWS</span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">OpenSearch</span>
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Vector DB</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Analytics</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-500 dark:text-slate-400">Esports Analytics</span>
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse animation-delay-200"></div>
                    <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse animation-delay-400"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Project 2 - Cloud Inventory Management */}
            <div className="group bg-gradient-to-br from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 border border-green-200 dark:border-green-800">
              <div className="relative h-48 bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl mb-2">📦</div>
                  <div className="text-white font-bold text-lg">Cloud Inventory</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                  Cloud Inventory Management
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Enterprise inventory system with ML-powered demand forecasting, built on AWS with React frontend.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AWS</span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">React</span>
                  <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Java</span>
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">ML</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-500 dark:text-slate-400">Enterprise Solution</span>
                  <span className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-3 py-1 rounded-full text-xs font-bold">🎯 Production Ready</span>
                </div>
              </div>
            </div>

            {/* Project 3 - Speech-to-Text Document Creation */}
            <div className="group bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:-rotate-1 border border-indigo-200 dark:border-indigo-800">
              <div className="relative h-48 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl mb-2">🎤</div>
                  <div className="text-white font-bold text-lg">Speech-to-Text</div>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-bold">🎯 90% Accuracy</span>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-300">
                  Speech-to-Text Document Creation
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Real-time speech recognition application with document creation, PDF conversion, text-to-speech, and advanced formatting features.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Python</span>
                  <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Speech Recognition</span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">PDF</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-500 dark:text-slate-400">Jan-May 2018</span>
                  <span className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-bold">🎯 90% Accuracy</span>
                </div>
              </div>
            </div>

            {/* Project 4 - Cruddr Micro-blogging */}
            <div className="group bg-gradient-to-br from-purple-50 to-pink-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 border border-purple-200 dark:border-purple-800">
              <div className="relative h-48 bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl mb-2">💬</div>
                  <div className="text-white font-bold text-lg">Cruddr</div>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-3 py-1 rounded-full text-xs font-bold">25% Cost Reduction</span>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                  Cruddr - Micro-blogging Platform
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Production-ready micro-blogging platform with 25% cost reduction and 30% performance improvement using AWS Fargate.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">React</span>
                  <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Python Flask</span>
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AWS Fargate</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Cost Optimization</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-500 dark:text-slate-400">Production Platform</span>
                  <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-3 py-1 rounded-full text-xs font-bold">🚀 Live</span>
                </div>
              </div>
            </div>

            {/* Project 5 - Healthcare Data Pipeline */}
            <div className="group bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:-rotate-1 border border-teal-200 dark:border-teal-800">
              <div className="relative h-48 bg-gradient-to-br from-teal-500 via-cyan-500 to-blue-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl mb-2">🏥</div>
                  <div className="text-white font-bold text-lg">Healthcare Pipeline</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors duration-300">
                  Automated Data Processing Pipeline
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  HIPAA-compliant healthcare analytics pipeline using Python and Azure, processing sensitive medical data with automated workflows.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Python</span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Azure</span>
                  <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Healthcare</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">HIPAA</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-500 dark:text-slate-400">Healthcare Analytics</span>
                  <span className="bg-gradient-to-r from-teal-600 to-cyan-600 text-white px-3 py-1 rounded-full text-xs font-bold">🔒 Secure</span>
                </div>
              </div>
            </div>

            {/* Project 6 - AR Tourism App */}
            <div className="group bg-gradient-to-br from-pink-50 to-rose-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 border border-pink-200 dark:border-pink-800">
              <div className="relative h-48 bg-gradient-to-br from-pink-500 via-rose-500 to-orange-500 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl mb-2">🗺️</div>
                  <div className="text-white font-bold text-lg">AR Tourism</div>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="bg-gradient-to-r from-pink-600 to-orange-600 text-white px-3 py-1 rounded-full text-xs font-bold">🎓 Academic</span>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                  AR Tourism App
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                  Augmented reality mobile application for tourism with interactive map views, real-time location tracking, and immersive AR experiences.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Adobe XD</span>
                  <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Swift</span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">Dart</span>
                  <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200">AR</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-500 dark:text-slate-400">Mobile AR Experience</span>
                  <span className="bg-gradient-to-r from-pink-600 to-orange-600 text-white px-3 py-1 rounded-full text-xs font-bold">📱 Mobile</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              📬 Get In Touch
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology. 💬
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md text-center">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">Email</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">Let's discuss opportunities</p>
              <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                <EMAIL>
              </a>
            </div>

            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md text-center">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">LinkedIn</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">Connect professionally</p>
              <a href="https://linkedin.com/in/neeharika-vemulapati" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                linkedin.com/in/neeharika-vemulapati
              </a>
            </div>

            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md text-center">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">GitHub</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">Check out my code</p>
              <a href="https://github.com/neeharve" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                github.com/neeharve
              </a>
            </div>
          </div>

          <div className="text-center mt-12">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">Ready to collaborate? 🚀</h3>
              <p className="mb-4">I'm currently open to new opportunities and exciting projects.</p>
              <a
                href="mailto:<EMAIL>"
                className="inline-block bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-slate-100 transition-colors duration-300"
              >
                Send me an email
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
