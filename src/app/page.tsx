"use client";

import { useState, useRef } from "react";

export default function Home() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const achievementsRef = useRef<HTMLDivElement>(null);

  const achievements = ['technovation', 'microsoft', 'guinness', 'sun', 'agile', 'academic'];

  const scrollToIndex = (index: number) => {
    if (achievementsRef.current) {
      const cardWidth = 320 + 32;
      achievementsRef.current.scrollTo({
        left: index * cardWidth,
        behavior: 'smooth'
      });
      setCurrentIndex(index);
    }
  };

  const goToPrevious = () => {
    const newIndex = Math.max(0, currentIndex - 2);
    scrollToIndex(newIndex);
  };

  const goToNext = () => {
    const maxIndex = Math.max(0, achievements.length - 2);
    const newIndex = Math.min(maxIndex, currentIndex + 2);
    scrollToIndex(newIndex);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm z-50 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-xl font-semibold text-slate-800 dark:text-white">
              Neeharika Vemulapati
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#about" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a>
              <a href="#experience" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Experience</a>
              <a href="#education" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Education</a>
              <a href="#achievements" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Achievements</a>
              <a href="#skills" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Skills</a>
              <a href="#projects" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Projects</a>
              <a href="#contact" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Contact</a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-40 h-40 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-1 animate-pulse hover:animate-spin transition-all duration-500 group">
                <div className="w-full h-full rounded-full overflow-hidden bg-slate-100 dark:bg-slate-800 hover:bg-white dark:hover:bg-slate-700 transition-colors duration-300">
                  <img
                    src="/neeharika-photo.jpg"
                    alt="Neeharika Vemulapati - Senior Software Engineer"
                    className="w-full h-full object-cover rounded-full group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-4 text-slate-900 dark:text-white animate-fade-in-up">
                Hi, I'm <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Neeharika Vemulapati</span>
                <span className="inline-block animate-bounce ml-2">👋</span>
              </h1>
            </div>

            <div className="space-y-4 mb-12 animate-fade-in-up animation-delay-500">
              <p className="text-xl sm:text-2xl text-slate-600 dark:text-slate-300 font-medium">
                💻 Senior Software Engineer
              </p>

              <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 px-6 py-3 rounded-full border border-blue-200 dark:border-blue-800">
                <span className="text-lg text-blue-700 dark:text-blue-300 font-semibold">
                  🏆 Microsoft AI Hackathon 2025 Winner
                </span>
                <span className="ml-2 animate-pulse">✨</span>
              </div>

              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed">
                🚀 Passionate about building innovative software solutions with expertise in
                <span className="font-semibold text-blue-600 dark:text-blue-400"> Java, Python, AWS</span>, and modern web technologies.
                Currently leading <span className="font-semibold text-purple-600 dark:text-purple-400">digital payments transformation initiatives</span> at CSAA Insurance Group.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up animation-delay-1000">
              <a
                href="#contact"
                className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="flex items-center justify-center">
                  📬 Get In Touch
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </a>
              <a
                href="#projects"
                className="group border-2 border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                <span className="flex items-center justify-center">
                  🚀 View Projects
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Work Experience Section with Alternating Layout */}
      <section id="experience" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Work Experience
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              A journey through innovative technology companies, building scalable solutions and leading digital transformation initiatives.
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-purple-600 hidden md:block"></div>

            <div className="space-y-12">
              {/* Experience 1 - CSAA Insurance Group - LEFT */}
              <div className="relative flex items-center group">
                {/* Left side content */}
                <div className="w-full md:w-5/12 md:pr-8">
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-700 p-6 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between mb-4">
                      <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium animate-pulse">Current</span>
                      <span className="text-sm text-slate-500 dark:text-slate-400">Feb 2025 - Present</span>
                    </div>
                    <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Senior Software Engineer</h3>
                    <p className="text-blue-600 dark:text-blue-400 font-semibold mb-4">CSAA Insurance Group</p>
                    <p className="text-slate-600 dark:text-slate-300 mb-4">
                      Leading digital payments transformation initiatives and developing scalable insurance technology solutions.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Java</span>
                      <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Spring Boot</span>
                      <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">AWS</span>
                    </div>
                  </div>
                </div>

                {/* Center timeline dot */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300 animate-pulse hidden md:block z-10"></div>

                {/* Right side - empty for left-aligned experience */}
                <div className="hidden md:block md:w-5/12"></div>
              </div>

              {/* Experience 2 - American Express (Recent) - RIGHT */}
              <div className="relative flex items-center group">
                {/* Left side - empty for right-aligned experience */}
                <div className="hidden md:block md:w-5/12"></div>

                {/* Center timeline dot */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white dark:border-slate-900 group-hover:scale-125 transition-transform duration-300 hidden md:block z-10"></div>

                {/* Right side content */}
                <div className="w-full md:w-5/12 md:pl-8">
                  <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <div className="flex items-center justify-between mb-4">
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">Recent</span>
                      <span className="text-sm text-slate-500 dark:text-slate-400">Jun 2024 - Jan 2025</span>
                    </div>
                    <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Software Development Engineer</h3>
                    <p className="text-blue-600 dark:text-blue-400 font-semibold mb-4">American Express</p>
                    <p className="text-slate-600 dark:text-slate-300 mb-4">
                      Developed enterprise-scale financial applications and payment processing systems serving millions of customers.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Java</span>
                      <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs">Python</span>
                      <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Spring Boot</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements & Awards Section with Carousel */}
      <section id="achievements" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Achievements & Awards
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Recognition for excellence, leadership, and outstanding contributions throughout my career.
            </p>
          </div>

          <div className="relative">
            {/* Navigation Arrows */}
            <button
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 rounded-full p-3 shadow-lg border border-slate-200 dark:border-slate-600 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={goToPrevious}
              disabled={currentIndex === 0}
            >
              <svg className="w-6 h-6 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 rounded-full p-3 shadow-lg border border-slate-200 dark:border-slate-600 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={goToNext}
              disabled={currentIndex >= achievements.length - 2}
            >
              <svg className="w-6 h-6 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Achievements Container */}
            <div
              ref={achievementsRef}
              className="flex gap-8 overflow-x-hidden scroll-smooth"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                width: '672px', // Exactly 2 cards: (320px * 2) + (32px gap) = 672px
                margin: '0 auto'
              }}
            >
              {/* Technovation Girls Gold Judge - Latest Achievement */}
              <div className="group bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-2 border-amber-300 dark:border-amber-700 relative overflow-hidden flex-shrink-0 w-80">
                {/* New badge */}
                <div className="absolute top-4 right-4 bg-gradient-to-r from-amber-600 to-yellow-600 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                  NEW
                </div>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Gold Judge Recognition</h3>
                  <div className="mb-4">
                    <span className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 px-3 py-1 rounded-full text-sm font-medium">2025</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Technovation Girls</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Global Technology Competition Judge
                  </p>
                  <div className="bg-amber-100 dark:bg-amber-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Judged 11+ technology projects, contributing to over 14,500 scores globally and empowering girls to become tech leaders"
                    </p>
                  </div>
                </div>
              </div>

              {/* Microsoft AI Hackathon */}
              <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-2 border-blue-300 dark:border-blue-700 relative overflow-hidden flex-shrink-0 w-80">
                <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                  WINNER
                </div>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Best Python Agent</h3>
                  <div className="mb-4">
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">2025</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    <strong>Microsoft AI Hackathon</strong>
                  </p>
                  <p className="text-slate-500 dark:text-slate-400 text-sm text-center">
                    Global AI Competition
                  </p>
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                    <p className="text-xs text-slate-600 dark:text-slate-300 text-center">
                      "Developed innovative AI agent using Python, recognized among thousands of global participants"
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
